{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/basira_framework/admin_dashboard/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport clsx from 'clsx';\nimport { \n  HomeIcon, \n  UsersIcon, \n  FolderIcon, \n  DocumentTextIcon, \n  ServerIcon, \n  CogIcon, \n  ChartBarIcon,\n  XMarkIcon,\n  Bars3Icon\n} from '@heroicons/react/24/outline';\nimport { NavItem } from '@/types';\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Users', href: '/users', icon: UsersIcon },\n  { name: 'Projects', href: '/projects', icon: FolderIcon },\n  { name: 'Logs & Monitoring', href: '/logs', icon: DocumentTextIcon },\n  { name: 'API Endpoints', href: '/api-monitor', icon: ServerIcon },\n  { name: 'Infrastructure', href: '/infrastructure', icon: ChartBarIcon },\n  { name: 'Setting<PERSON>', href: '/settings', icon: CogIcon },\n];\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <button\n          type=\"button\"\n          className=\"p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n        >\n          <span className=\"sr-only\">Open sidebar</span>\n          {isMobileMenuOpen ? (\n            <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          ) : (\n            <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n          )}\n        </button>\n      </div>\n\n      {/* Mobile menu */}\n      <div\n        className={clsx(\n          'fixed inset-0 z-40 lg:hidden bg-background',\n          isMobileMenuOpen ? 'block' : 'hidden'\n        )}\n      >\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setIsMobileMenuOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 max-w-xs w-full bg-card shadow-lg\">\n          <div className=\"flex flex-col h-full\">\n            <div className=\"flex items-center justify-between h-16 px-4 border-b border-border\">\n              <div className=\"flex items-center\">\n                <span className=\"text-xl font-semibold text-primary\">BasiraOne</span>\n              </div>\n              <button\n                type=\"button\"\n                className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                <span className=\"sr-only\">Close sidebar</span>\n                <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n              </button>\n            </div>\n            <div className=\"flex-1 overflow-y-auto py-4 px-2\">\n              <nav className=\"space-y-1\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className={clsx(\n                      pathname === item.href || pathname.startsWith(`${item.href}/`)\n                        ? 'bg-primary text-primary-foreground'\n                        : 'text-foreground hover:bg-muted',\n                      'group flex items-center px-3 py-2 text-sm font-medium rounded-md'\n                    )}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <item.icon\n                      className={clsx(\n                        pathname === item.href || pathname.startsWith(`${item.href}/`)\n                          ? 'text-primary-foreground'\n                          : 'text-muted-foreground group-hover:text-foreground',\n                        'mr-3 h-5 w-5 flex-shrink-0'\n                      )}\n                      aria-hidden=\"true\"\n                    />\n                    {item.name}\n                  </Link>\n                ))}\n              </nav>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div\n        className={clsx(\n          'hidden lg:fixed lg:inset-y-0 lg:flex lg:flex-col lg:w-64 lg:border-r lg:border-border lg:bg-card lg:pt-5',\n          className\n        )}\n      >\n        <div className=\"flex items-center justify-center h-16 px-4\">\n          <span className=\"text-xl font-semibold text-primary\">BasiraOne</span>\n        </div>\n        <div className=\"flex-1 flex flex-col overflow-y-auto pt-5 px-3\">\n          <nav className=\"flex-1 space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={clsx(\n                  pathname === item.href || pathname.startsWith(`${item.href}/`)\n                    ? 'bg-primary text-primary-foreground'\n                    : 'text-foreground hover:bg-muted',\n                  'group flex items-center px-3 py-2 text-sm font-medium rounded-md'\n                )}\n              >\n                <item.icon\n                  className={clsx(\n                    pathname === item.href || pathname.startsWith(`${item.href}/`)\n                      ? 'text-primary-foreground'\n                      : 'text-muted-foreground group-hover:text-foreground',\n                    'mr-3 h-5 w-5 flex-shrink-0'\n                  )}\n                  aria-hidden=\"true\"\n                />\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAmBA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;IAAC;IACjD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,sNAAA,CAAA,aAAU;IAAC;IACxD;QAAE,MAAM;QAAqB,MAAM;QAAS,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACnE;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM,sNAAA,CAAA,aAAU;IAAC;IAChE;QAAE,MAAM;QAAkB,MAAM;QAAmB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,gNAAA,CAAA,UAAO;IAAC;CACtD;AAMM,SAAS,QAAQ,KAA2B;QAA3B,EAAE,SAAS,EAAgB,GAA3B;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,oBAAoB,CAAC;;sCAEpC,6LAAC;4BAAK,WAAU;sCAAU;;;;;;wBACzB,iCACC,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;4BAAU,eAAY;;;;;iDAE3C,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;4BAAU,eAAY;;;;;;;;;;;;;;;;;0BAMjD,6LAAC;gBACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,8CACA,mBAAmB,UAAU;;kCAG/B,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,oBAAoB;;;;;;kCAC5F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;sDAEvD,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,oBAAoB;;8DAEnC,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC,QACvD,uCACA,kCACJ;gDAEF,SAAS,IAAM,oBAAoB;;kEAEnC,6LAAC,KAAK,IAAI;wDACR,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC,QACvD,4BACA,qDACJ;wDAEF,eAAY;;;;;;oDAEb,KAAK,IAAI;;+CAnBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA6B5B,6LAAC;gBACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,4GACA;;kCAGF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAqC;;;;;;;;;;;kCAEvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC,QACvD,uCACA,kCACJ;;sDAGF,6LAAC,KAAK,IAAI;4CACR,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC,QACvD,4BACA,qDACJ;4CAEF,eAAY;;;;;;wCAEb,KAAK,IAAI;;mCAlBL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;AA0B9B;GArHgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/basira_framework/admin_dashboard/src/hooks/useTheme.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ntype Theme = 'light' | 'dark' | 'system';\n\nexport function useTheme() {\n  const [theme, setTheme] = useState<Theme>('system');\n\n  useEffect(() => {\n    // Load theme from localStorage on mount\n    const savedTheme = localStorage.getItem('theme') as Theme | null;\n    if (savedTheme) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    // Apply theme changes\n    const root = window.document.documentElement;\n    \n    if (theme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches\n        ? 'dark'\n        : 'light';\n      \n      root.classList.remove('light', 'dark');\n      root.classList.add(systemTheme);\n    } else {\n      root.classList.remove('light', 'dark');\n      root.classList.add(theme);\n    }\n\n    // Save theme to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = () => {\n      if (theme === 'system') {\n        const root = window.document.documentElement;\n        root.classList.remove('light', 'dark');\n        root.classList.add(mediaQuery.matches ? 'dark' : 'light');\n      }\n    };\n    \n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme]);\n\n  return { theme, setTheme };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAMO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,wCAAwC;YACxC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;YACX;QACF;6BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,sBAAsB;YACtB,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;YAE5C,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GACzE,SACA;gBAEJ,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;gBAC/B,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;gBAC/B,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB;YAEA,6BAA6B;YAC7B,aAAa,OAAO,CAAC,SAAS;QAChC;6BAAG;QAAC;KAAM;IAEV,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM;mDAAe;oBACnB,IAAI,UAAU,UAAU;wBACtB,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;wBAC5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;wBAC/B,KAAK,SAAS,CAAC,GAAG,CAAC,WAAW,OAAO,GAAG,SAAS;oBACnD;gBACF;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;sCAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;6BAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAO;IAAS;AAC3B;GAhDgB", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/basira_framework/admin_dashboard/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { \n  UserCircleIcon, \n  CogIcon, \n  ArrowRightOnRectangleIcon,\n  SunIcon,\n  MoonIcon,\n  ComputerDesktopIcon\n} from '@heroicons/react/24/outline';\nimport { useTheme } from '@/hooks/useTheme';\n\ninterface HeaderProps {\n  className?: string;\n}\n\nexport function Header({ className }: HeaderProps) {\n  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);\n  const [isThemeMenuOpen, setIsThemeMenuOpen] = useState(false);\n  const profileMenuRef = useRef<HTMLDivElement>(null);\n  const themeMenuRef = useRef<HTMLDivElement>(null);\n  const { theme, setTheme } = useTheme();\n\n  // Mock user data - replace with actual auth context\n  const user = {\n    name: 'Admin User',\n    email: '<EMAIL>',\n    avatar: null,\n    role: 'admin' as const\n  };\n\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {\n        setIsProfileMenuOpen(false);\n      }\n      if (themeMenuRef.current && !themeMenuRef.current.contains(event.target as Node)) {\n        setIsThemeMenuOpen(false);\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const themeOptions = [\n    { name: 'Light', value: 'light', icon: SunIcon },\n    { name: 'Dark', value: 'dark', icon: MoonIcon },\n    { name: 'System', value: 'system', icon: ComputerDesktopIcon },\n  ];\n\n  return (\n    <header className={`bg-card border-b border-border ${className}`}>\n      <div className=\"flex items-center justify-between h-16 px-4 lg:px-6\">\n        {/* Left side - could add breadcrumbs or search here */}\n        <div className=\"flex items-center\">\n          <h1 className=\"text-lg font-semibold text-foreground lg:ml-0 ml-12\">\n            Admin Dashboard\n          </h1>\n        </div>\n\n        {/* Right side - theme toggle and user menu */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Theme toggle */}\n          <div className=\"relative\" ref={themeMenuRef}>\n            <button\n              type=\"button\"\n              className=\"p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring\"\n              onClick={() => setIsThemeMenuOpen(!isThemeMenuOpen)}\n            >\n              <span className=\"sr-only\">Toggle theme</span>\n              {theme === 'light' && <SunIcon className=\"h-5 w-5\" />}\n              {theme === 'dark' && <MoonIcon className=\"h-5 w-5\" />}\n              {theme === 'system' && <ComputerDesktopIcon className=\"h-5 w-5\" />}\n            </button>\n\n            {isThemeMenuOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50\">\n                <div className=\"py-1\">\n                  {themeOptions.map((option) => (\n                    <button\n                      key={option.value}\n                      className=\"flex items-center w-full px-4 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground\"\n                      onClick={() => {\n                        setTheme(option.value as 'light' | 'dark' | 'system');\n                        setIsThemeMenuOpen(false);\n                      }}\n                    >\n                      <option.icon className=\"mr-3 h-4 w-4\" />\n                      {option.name}\n                      {theme === option.value && (\n                        <span className=\"ml-auto text-primary\">✓</span>\n                      )}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* User menu */}\n          <div className=\"relative\" ref={profileMenuRef}>\n            <button\n              type=\"button\"\n              className=\"flex items-center space-x-3 p-2 rounded-md text-sm hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring\"\n              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}\n            >\n              <div className=\"flex items-center space-x-2\">\n                {user.avatar ? (\n                  <img\n                    className=\"h-8 w-8 rounded-full\"\n                    src={user.avatar}\n                    alt={user.name}\n                  />\n                ) : (\n                  <UserCircleIcon className=\"h-8 w-8 text-muted-foreground\" />\n                )}\n                <div className=\"hidden md:block text-left\">\n                  <div className=\"text-sm font-medium text-foreground\">{user.name}</div>\n                  <div className=\"text-xs text-muted-foreground\">{user.role}</div>\n                </div>\n              </div>\n            </button>\n\n            {isProfileMenuOpen && (\n              <div className=\"absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50\">\n                <div className=\"py-1\">\n                  <div className=\"px-4 py-2 border-b border-border\">\n                    <div className=\"text-sm font-medium text-popover-foreground\">{user.name}</div>\n                    <div className=\"text-xs text-muted-foreground\">{user.email}</div>\n                  </div>\n                  <Link\n                    href=\"/profile\"\n                    className=\"flex items-center px-4 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground\"\n                    onClick={() => setIsProfileMenuOpen(false)}\n                  >\n                    <UserCircleIcon className=\"mr-3 h-4 w-4\" />\n                    Profile\n                  </Link>\n                  <Link\n                    href=\"/settings\"\n                    className=\"flex items-center px-4 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground\"\n                    onClick={() => setIsProfileMenuOpen(false)}\n                  >\n                    <CogIcon className=\"mr-3 h-4 w-4\" />\n                    Settings\n                  </Link>\n                  <button\n                    className=\"flex items-center w-full px-4 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground\"\n                    onClick={() => {\n                      // Handle logout\n                      setIsProfileMenuOpen(false);\n                    }}\n                  >\n                    <ArrowRightOnRectangleIcon className=\"mr-3 h-4 w-4\" />\n                    Sign out\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAZA;;;;;AAkBO,SAAS,OAAO,KAA0B;QAA1B,EAAE,SAAS,EAAe,GAA1B;;IACrB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEnC,oDAAoD;IACpD,MAAM,OAAO;QACX,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;IACR;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBACpF,qBAAqB;gBACvB;gBACA,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAChF,mBAAmB;gBACrB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB;YAAE,MAAM;YAAS,OAAO;YAAS,MAAM,gNAAA,CAAA,UAAO;QAAC;QAC/C;YAAE,MAAM;YAAQ,OAAO;YAAQ,MAAM,kNAAA,CAAA,WAAQ;QAAC;QAC9C;YAAE,MAAM;YAAU,OAAO;YAAU,MAAM,wOAAA,CAAA,sBAAmB;QAAC;KAC9D;IAED,qBACE,6LAAC;QAAO,WAAW,AAAC,kCAA2C,OAAV;kBACnD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAsD;;;;;;;;;;;8BAMtE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;4BAAW,KAAK;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,mBAAmB,CAAC;;sDAEnC,6LAAC;4CAAK,WAAU;sDAAU;;;;;;wCACzB,UAAU,yBAAW,6LAAC,gNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCACxC,UAAU,wBAAU,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACxC,UAAU,0BAAY,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;;gCAGvD,iCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC;gDAEC,WAAU;gDACV,SAAS;oDACP,SAAS,OAAO,KAAK;oDACrB,mBAAmB;gDACrB;;kEAEA,6LAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;oDACtB,OAAO,IAAI;oDACX,UAAU,OAAO,KAAK,kBACrB,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;;+CAVpC,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;sCAoB7B,6LAAC;4BAAI,WAAU;4BAAW,KAAK;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,qBAAqB,CAAC;8CAErC,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,MAAM,iBACV,6LAAC;gDACC,WAAU;gDACV,KAAK,KAAK,MAAM;gDAChB,KAAK,KAAK,IAAI;;;;;qEAGhB,6LAAC,8NAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAE5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAuC,KAAK,IAAI;;;;;;kEAC/D,6LAAC;wDAAI,WAAU;kEAAiC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;gCAK9D,mCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA+C,KAAK,IAAI;;;;;;kEACvE,6LAAC;wDAAI,WAAU;kEAAiC,KAAK,KAAK;;;;;;;;;;;;0DAE5D,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,qBAAqB;;kEAEpC,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG7C,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,qBAAqB;;kEAEpC,6LAAC,gNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGtC,6LAAC;gDACC,WAAU;gDACV,SAAS;oDACP,gBAAgB;oDAChB,qBAAqB;gDACvB;;kEAEA,6LAAC,oPAAA,CAAA,4BAAyB;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1E;GAvJgB;;QAKc,2HAAA,CAAA,WAAQ;;;KALtB", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/basira_framework/admin_dashboard/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface DashboardLayoutProps {\n  children: ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Sidebar />\n      \n      <div className=\"lg:pl-64\">\n        <Header />\n        \n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;IAC9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;;;;;kCAEP,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;KAhBgB", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/basira_framework/admin_dashboard/src/components/ui/Card.tsx"], "sourcesContent": ["import { ReactNode } from 'react';\nimport clsx from 'clsx';\n\ninterface CardProps {\n  children: ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n}\n\nexport function Card({ children, className, padding = 'md' }: CardProps) {\n  const paddingClasses = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8',\n  };\n\n  return (\n    <div\n      className={clsx(\n        'bg-card border border-border rounded-lg shadow-sm',\n        paddingClasses[padding],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n}\n\ninterface CardHeaderProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport function CardHeader({ children, className }: CardHeaderProps) {\n  return (\n    <div className={clsx('mb-4', className)}>\n      {children}\n    </div>\n  );\n}\n\ninterface CardTitleProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport function CardTitle({ children, className }: CardTitleProps) {\n  return (\n    <h3 className={clsx('text-lg font-semibold text-card-foreground', className)}>\n      {children}\n    </h3>\n  );\n}\n\ninterface CardContentProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport function CardContent({ children, className }: CardContentProps) {\n  return (\n    <div className={clsx('text-card-foreground', className)}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAQO,SAAS,KAAK,KAAkD;QAAlD,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,IAAI,EAAa,GAAlD;IACnB,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EACZ,qDACA,cAAc,CAAC,QAAQ,EACvB;kBAGD;;;;;;AAGP;KAnBgB;AA0BT,SAAS,WAAW,KAAwC;QAAxC,EAAE,QAAQ,EAAE,SAAS,EAAmB,GAAxC;IACzB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ;kBAC1B;;;;;;AAGP;MANgB;AAaT,SAAS,UAAU,KAAuC;QAAvC,EAAE,QAAQ,EAAE,SAAS,EAAkB,GAAvC;IACxB,qBACE,6LAAC;QAAG,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,8CAA8C;kBAC/D;;;;;;AAGP;MANgB;AAaT,SAAS,YAAY,KAAyC;QAAzC,EAAE,QAAQ,EAAE,SAAS,EAAoB,GAAzC;IAC1B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,wBAAwB;kBAC1C;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projecten/basira_one/basira_framework/admin_dashboard/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { DashboardLayout } from '@/components/layout/DashboardLayout';\nimport { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';\nimport { \n  UsersIcon, \n  FolderIcon, \n  ServerIcon, \n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\nexport default function DashboardPage() {\n  // Mock data - replace with actual API calls\n  const stats = [\n    {\n      name: 'Total Users',\n      value: '1,234',\n      change: '+12%',\n      changeType: 'positive' as const,\n      icon: UsersIcon,\n    },\n    {\n      name: 'Active Projects',\n      value: '8',\n      change: '+2',\n      changeType: 'positive' as const,\n      icon: FolderIcon,\n    },\n    {\n      name: 'API Endpoints',\n      value: '156',\n      change: '+8',\n      changeType: 'positive' as const,\n      icon: ServerIcon,\n    },\n    {\n      name: 'System Health',\n      value: '99.9%',\n      change: '-0.1%',\n      changeType: 'negative' as const,\n      icon: CheckCircleIcon,\n    },\n  ];\n\n  const recentActivity = [\n    {\n      id: 1,\n      type: 'user',\n      message: 'New user registered: <EMAIL>',\n      timestamp: '2 minutes ago',\n      status: 'info',\n    },\n    {\n      id: 2,\n      type: 'project',\n      message: 'Project \"E-commerce API\" deployed successfully',\n      timestamp: '15 minutes ago',\n      status: 'success',\n    },\n    {\n      id: 3,\n      type: 'error',\n      message: 'High error rate detected on /api/payments endpoint',\n      timestamp: '1 hour ago',\n      status: 'error',\n    },\n    {\n      id: 4,\n      type: 'system',\n      message: 'Scheduled maintenance completed',\n      timestamp: '3 hours ago',\n      status: 'success',\n    },\n  ];\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'success':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'error':\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-red-500\" />;\n      default:\n        return <ClockIcon className=\"h-5 w-5 text-blue-500\" />;\n    }\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Page header */}\n        <div>\n          <h1 className=\"text-2xl font-bold text-foreground\">Dashboard</h1>\n          <p className=\"text-muted-foreground\">\n            Welcome to your BasiraOne admin dashboard\n          </p>\n        </div>\n\n        {/* Stats grid */}\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n          {stats.map((stat) => (\n            <Card key={stat.name}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <stat.icon className=\"h-8 w-8 text-muted-foreground\" />\n                  </div>\n                  <div className=\"ml-4 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-muted-foreground truncate\">\n                        {stat.name}\n                      </dt>\n                      <dd className=\"flex items-baseline\">\n                        <div className=\"text-2xl font-semibold text-foreground\">\n                          {stat.value}\n                        </div>\n                        <div\n                          className={`ml-2 flex items-baseline text-sm font-semibold ${\n                            stat.changeType === 'positive'\n                              ? 'text-green-600'\n                              : 'text-red-600'\n                          }`}\n                        >\n                          {stat.change}\n                        </div>\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Recent activity */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Recent Activity</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentActivity.map((activity) => (\n                  <div key={activity.id} className=\"flex items-start space-x-3\">\n                    <div className=\"flex-shrink-0\">\n                      {getStatusIcon(activity.status)}\n                    </div>\n                    <div className=\"min-w-0 flex-1\">\n                      <p className=\"text-sm text-foreground\">{activity.message}</p>\n                      <p className=\"text-xs text-muted-foreground\">\n                        {activity.timestamp}\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>System Status</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-foreground\">API Gateway</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-2 w-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm text-muted-foreground\">Online</span>\n                  </div>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-foreground\">Database</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-2 w-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm text-muted-foreground\">Online</span>\n                  </div>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-foreground\">Cache Server</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-2 w-2 bg-yellow-500 rounded-full\"></div>\n                    <span className=\"text-sm text-muted-foreground\">Degraded</span>\n                  </div>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-foreground\">File Storage</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-2 w-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-sm text-muted-foreground\">Online</span>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAae,SAAS;IACtB,4CAA4C;IAC5C,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,oNAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,sNAAA,CAAA,aAAU;QAClB;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,sNAAA,CAAA,aAAU;QAClB;QACA;YACE,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM,gOAAA,CAAA,kBAAe;QACvB;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,IAAI;YAC<PERSON>,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW;YACX,QAAQ;QACV;KACD;IAED,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,gOAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,gPAAA,CAAA,0BAAuB;oBAAC,WAAU;;;;;;YAC5C;gBACE,qBAAO,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,qBACE,6LAAC,kJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EACZ,KAAK,KAAK;;;;;;0EAEb,6LAAC;gEACC,WAAW,AAAC,kDAIX,OAHC,KAAK,UAAU,KAAK,aAChB,mBACA;0EAGL,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtBf,KAAK,IAAI;;;;;;;;;;8BAkCxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,yBACnB,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC;wDAAI,WAAU;kEACZ,cAAc,SAAS,MAAM;;;;;;kEAEhC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAA2B,SAAS,OAAO;;;;;;0EACxD,6LAAC;gEAAE,WAAU;0EACV,SAAS,SAAS;;;;;;;;;;;;;+CAPf,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;sCAgB7B,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpE;KA5LwB", "debugId": null}}]}