// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user' | 'viewer';
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

export interface UserRole {
  id: string;
  name: string;
  permissions: string[];
}

// Project types
export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'maintenance';
  backendUrl: string;
  healthCheckUrl?: string;
  createdAt: string;
  updatedAt: string;
  lastHealthCheck?: string;
  isHealthy?: boolean;
}

// Log types
export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  projectId: string;
  projectName: string;
  source?: string;
  metadata?: Record<string, any>;
}

export interface LogFilter {
  startDate?: string;
  endDate?: string;
  level?: LogEntry['level'];
  projectId?: string;
  search?: string;
}

// API Monitor types
export interface ApiEndpoint {
  id: string;
  projectId: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description?: string;
  lastChecked?: string;
  status?: number;
  responseTime?: number;
  successRate?: number;
}

export interface ApiMetrics {
  endpointId: string;
  timestamp: string;
  statusCode: number;
  responseTime: number;
  success: boolean;
}

// Infrastructure types
export interface HealthCheck {
  id: string;
  projectId: string;
  url: string;
  status: 'online' | 'offline' | 'degraded';
  responseTime?: number;
  lastCheck: string;
  uptime?: number;
}

// Settings types
export interface GlobalSettings {
  siteName: string;
  siteDescription: string;
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'nl';
  notifications: {
    email: boolean;
    browser: boolean;
    slack?: boolean;
  };
  monitoring: {
    healthCheckInterval: number;
    logRetentionDays: number;
    alertThresholds: {
      responseTime: number;
      errorRate: number;
    };
  };
}

export interface ProjectSettings {
  projectId: string;
  apiKeys: Record<string, string>;
  environment: 'development' | 'staging' | 'production';
  customSettings: Record<string, any>;
}

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current?: boolean;
  children?: NavItem[];
}

// Auth types
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: User['role'];
  avatar?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthState {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Chart data types
export interface ChartDataPoint {
  timestamp: string;
  value: number;
  label?: string;
}

export interface ActivityMetrics {
  requests: ChartDataPoint[];
  errors: ChartDataPoint[];
  responseTime: ChartDataPoint[];
}
