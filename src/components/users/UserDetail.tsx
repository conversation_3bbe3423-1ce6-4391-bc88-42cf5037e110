'use client';

import { User } from '@/types';
import { Button } from '@/components/ui/Button';
import { XMarkIcon, UserCircleIcon } from '@heroicons/react/24/outline';

interface UserDetailProps {
  user: User | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (user: User) => void;
}

export function UserDetail({ user, isOpen, onClose, onEdit }: UserDetailProps) {
  if (!isOpen || !user) return null;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose} />
        
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-card shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-card-foreground">
              User Details
            </h3>
            <button
              onClick={onClose}
              className="text-muted-foreground hover:text-foreground"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="flex flex-col items-center mb-6">
            {user.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="h-24 w-24 rounded-full object-cover"
              />
            ) : (
              <UserCircleIcon className="h-24 w-24 text-muted-foreground" />
            )}
            <h2 className="mt-2 text-xl font-semibold text-card-foreground">{user.name}</h2>
            <p className="text-muted-foreground">{user.email}</p>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">Role</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                user.role === 'admin' 
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' 
                  : user.role === 'user'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}>
                {user.role}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">Status</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                user.isActive 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' 
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
              }`}>
                {user.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">Created</span>
              <span className="text-sm text-card-foreground">{formatDate(user.createdAt)}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-sm font-medium text-muted-foreground">Last Updated</span>
              <span className="text-sm text-card-foreground">{formatDate(user.updatedAt)}</span>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
            <Button
              type="button"
              onClick={() => onEdit(user)}
            >
              Edit User
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
